spring:
  datasource:
    url: ${MMS_STORE_SECRET_db_url}
    username: ${MMS_STORE_SECRET_db_user}
    password: ${MMS_STORE_SECRET_db_password}
  jpa:
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
jwt:
  sign:
    key: ${MMS_STORE_SECRET_jwt_app_secret_key}

springdoc:
  swagger-ui:
    operations-sorter: method
  writer-with-default-pretty-printer: true

custom-domain:
  little-mall-domain: ${MMS_STORE_CONFIG_little_mall_domain}
  gcp:
    service-account: ${MMS_STORE_SECRET_little_mall_gcp_cert_manager_service_account}
    c-name: ${MMS_STORE_CONFIG_gcp_c_name}
    cert-map-name: ${MMS_STORE_CONFIG_cert_map_name}

little-mall:
  base-url: ${MMS_STORE_CONFIG_little_mall_base_url}
  default-store-logo: ${MMS_STORE_CONFIG_little_mall_default_store_logo}
  jwt:
    env: ${MMS_STORE_CONFIG_little_mall_jwt_env:DEV}
    private:
      key: ${MMS_STORE_SECRET_little_mall_jwt_private_key}
    public:
      key: ${MMS_STORE_SECRET_little_mall_jwt_public_key}

mms-product:
  base-url: ${MMS_STORE_CONFIG_mms_product_base_url}

mms-user:
  base-url: ${MMS_STORE_CONFIG_mms_user_base_url}

image-upload:
  base-url: ${MMS_STORE_CONFIG_image_upload_base_url}

mms-chat:
  system-param-segment: ${MMS_STORE_CONFIG_mms_chat_system_param_segment}
  white-list-cache-ttl: ${MMS_STORE_CONFIG_mms_chat_white-list-cache-ttl}

hktv-group-chat:
  base-url: ${MMS_STORE_CONFIG_hktv_group_chat_base_url}
  jwt:
    private:
      key: ${MMS_STORE_SECRET_hktv_group_chat_jwt_private_key}

customer-chat:
  base-url: ${MMS_STORE_CONFIG_customer_chat_base_url}

hybris:
  base-url: ${MMS_STORE_CONFIG_hybris_base_url}
  jwt:
    private:
      key: ${MMS_STORE_SECRET_hybris_jwt_private_key}

affiliate:
  impact:
    url: ${MMS_STORE_CONFIG_affiliate_impact_url}
    path:
      catalogs: ${MMS_STORE_CONFIG_affiliate_impact_catalogs_path}
    username: ${MMS_STORE_CONFIG_affiliate_impact_username}
    password: ${MMS_STORE_CONFIG_affiliate_impact_token}

kutt:
  base-url: ${MMS_STORE_CONFIG_kutt_base_url}
  api:
    hktv-key: ${MMS_STORE_SECRET_kutt_api_hktv_key}
    the-place-key: ${MMS_STORE_SECRET_kutt_api_the_place_key}

# grafana endpoint
management:
  endpoint:
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: prometheus,health

#Notification
notification:
 host: ${MMS_STORE_CONFIG_message_center_url}

#mdb
mdb:
 virtual-store-url: ${MMS_STORE_CONFIG_mdb_virtual_store_url}
 jwt:
   key: ${MMS_STORE_SECRET_jwt_mdb_key}

#Image validation
image:
  validation:
    max-file-size: ${MMS_STORE_CONFIG_image_validation_max_file_size}
    allowed-extensions: ${MMS_STORE_CONFIG_image_validation_allowed_extensions}
    max-file-name-length: ${MMS_STORE_CONFIG_image_validation_max_file_name_length}

# Database query logging configuration
database:
  query:
    slow-query-threshold-ms: ${MMS_STORE_CONFIG_database_query_slow_query_threshold_ms:100}
