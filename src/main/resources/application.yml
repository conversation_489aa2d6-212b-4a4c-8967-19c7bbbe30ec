spring:
  application:
    name: @project.artifactId@
  profiles:
    active: ${MMS_STORE_CONFIG_active_profile:local}
  jackson:
    deserialization:
      read-enums-using-to-string: true
    serialization:
      write-enums-using-to-string: true
  security:
    user:
      name: ${random.value}
      password: ${random.value}
  messages:
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  datasource:
      driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    open-in-view: false
    hibernate:
      ddl-auto: none

# Database query logging configuration
database:
  query:
    slow-query-threshold-ms: 100

server:
  port: 8080
  servlet:
    context-path: /store

management:
  info:
    git:
      enabled: true
      mode: full
  endpoints:
    web:
      exposure:
        include: "health,info"
springdoc:
  swagger-ui:
    enabled: true

build:
  version: @project.version@

image:
  validation:
    max-file-size: 8MB
    allowed-extensions: jpg,jpeg,png,gif
    max-file-name-length: 255
