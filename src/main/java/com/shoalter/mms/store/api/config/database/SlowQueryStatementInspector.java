package com.shoalter.mms.store.api.config.database;

import lombok.extern.slf4j.Slf4j;
import org.hibernate.resource.jdbc.spi.StatementInspector;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Hibernate StatementInspector to capture and potentially modify SQL statements.
 * This works in conjunction with SlowQueryLoggingInterceptor to provide comprehensive query logging.
 */
@Slf4j
@Component
public class SlowQueryStatementInspector implements StatementInspector {

    @Value("${database.query.slow-query-threshold-ms:100}")
    private long slowQueryThresholdMs;

    private final ThreadLocal<QueryContext> queryContext = new ThreadLocal<>();

    @Override
    public String inspect(String sql) {
        // Store the SQL and start time for this thread
        queryContext.set(new QueryContext(sql, System.currentTimeMillis()));
        
        // Log the SQL being executed (optional - can be disabled in production)
        if (log.isDebugEnabled()) {
            log.debug("Executing SQL: {}", sql);
        }
        
        return sql;
    }

    /**
     * This method should be called after query execution to log slow queries.
     * It's called from the SlowQueryLoggingInterceptor.
     */
    public void logSlowQueryIfNeeded() {
        QueryContext context = queryContext.get();
        if (context != null) {
            long executionTime = System.currentTimeMillis() - context.getStartTime();
            
            if (executionTime > slowQueryThresholdMs) {
                log.warn("SLOW QUERY DETECTED - Execution time: {}ms (threshold: {}ms)\nSQL: {}", 
                        executionTime, slowQueryThresholdMs, context.getSql());
            }
            
            queryContext.remove();
        }
    }

    /**
     * Get the current query context for this thread
     */
    public QueryContext getCurrentQueryContext() {
        return queryContext.get();
    }

    /**
     * Clear the query context for this thread
     */
    public void clearQueryContext() {
        queryContext.remove();
    }

    /**
     * Inner class to hold query context information
     */
    public static class QueryContext {
        private final String sql;
        private final long startTime;

        public QueryContext(String sql, long startTime) {
            this.sql = sql;
            this.startTime = startTime;
        }

        public String getSql() {
            return sql;
        }

        public long getStartTime() {
            return startTime;
        }
    }
}
