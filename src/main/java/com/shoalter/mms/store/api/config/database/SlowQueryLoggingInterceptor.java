package com.shoalter.mms.store.api.config.database;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.EmptyInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Hibernate interceptor to log slow SQL queries.
 * Works in conjunction with SlowQueryStatementInspector to provide comprehensive query logging.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SlowQueryLoggingInterceptor extends EmptyInterceptor {

    @Autowired(required = false)
    private SlowQueryStatementInspector statementInspector;

    @Override
    public void afterTransactionCompletion(org.hibernate.Transaction tx) {
        if (statementInspector != null) {
            statementInspector.logSlowQueryIfNeeded();
        }
        super.afterTransactionCompletion(tx);
    }

    @Override
    public void onCollectionUpdate(Object collection, java.io.Serializable key) {
        if (statementInspector != null) {
            statementInspector.logSlowQueryIfNeeded();
        }
        super.onCollectionUpdate(collection, key);
    }

    @Override
    public void onCollectionRemove(Object collection, java.io.Serializable key) {
        if (statementInspector != null) {
            statementInspector.logSlowQueryIfNeeded();
        }
        super.onCollectionRemove(collection, key);
    }

    @Override
    public void onCollectionRecreate(Object collection, java.io.Serializable key) {
        if (statementInspector != null) {
            statementInspector.logSlowQueryIfNeeded();
        }
        super.onCollectionRecreate(collection, key);
    }

    @Override
    public boolean onLoad(Object entity, java.io.Serializable id, Object[] state, String[] propertyNames, org.hibernate.type.Type[] types) {
        if (statementInspector != null) {
            statementInspector.logSlowQueryIfNeeded();
        }
        return super.onLoad(entity, id, state, propertyNames, types);
    }

    @Override
    public boolean onSave(Object entity, java.io.Serializable id, Object[] state, String[] propertyNames, org.hibernate.type.Type[] types) {
        if (statementInspector != null) {
            statementInspector.logSlowQueryIfNeeded();
        }
        return super.onSave(entity, id, state, propertyNames, types);
    }
}
