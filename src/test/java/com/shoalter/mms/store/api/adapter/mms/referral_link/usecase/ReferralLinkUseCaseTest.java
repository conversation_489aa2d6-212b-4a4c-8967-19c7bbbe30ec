package com.shoalter.mms.store.api.adapter.mms.referral_link.usecase;

import com.shoalter.mms.store.api.adapter.controller.dto.ResponseDto;
import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.kutt.KuttApiAdapter;
import com.shoalter.mms.store.api.adapter.kutt.dto.ReferralLinkDetailResponseDto;
import com.shoalter.mms.store.api.adapter.kutt.dto.ReferralLinkPageResponseDto;
import com.shoalter.mms.store.api.adapter.kutt.dto.ShortLinkRequestDto;
import com.shoalter.mms.store.api.adapter.mms.helper.CacheHelper;
import com.shoalter.mms.store.api.adapter.mms.product.dto.PageResult;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkCreateRequest;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkExportRequest;
import com.shoalter.mms.store.api.adapter.mms.referral_link.dto.ReferralLinkPageResponse;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindStoresResponse;
import com.shoalter.mms.store.api.adapter.mms.store.usecase.FindStoresUseCase;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.SystemParamEntity;
import com.shoalter.mms.store.api.enums.BuCodeEnum;
import com.shoalter.mms.store.api.enums.ReferralLinkExcelColumnEnum;
import com.shoalter.mms.store.api.enums.ResponseStatusCode;
import com.shoalter.mms.store.api.enums.SystemParamEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReferralLinkUseCaseTest {

    @Mock
    private MessageSource messageSource;

    @Mock
    private KuttApiAdapter kuttApiAdapter;

    @Mock
    private FindStoresUseCase findStoresUseCase;

    @Mock
    private CacheHelper cacheHelper;

    @InjectMocks
    private ReferralLinkUseCase referralLinkUseCase;

    private UserDto userDto;
    private FindStoresResponse storeResponse;
    private ReferralLinkCreateRequest createRequest;
    private ReferralLinkDetailResponseDto kuttResponse;

    @BeforeEach
    void setUp() {
        userDto = UserDto.builder()
            .userId(1)
            .build();

        storeResponse = FindStoresResponse.builder()
            .storeFrontStoreCode("H000001")
            .busUnitCode("HKTV")
            .build();

        createRequest = new ReferralLinkCreateRequest();
        createRequest.setStorefrontStoreCode("H000001");
        createRequest.setPageUrl("https://www.hktvmall.com/s/H000001");

        kuttResponse = new ReferralLinkDetailResponseDto();
        kuttResponse.setId("test-id");
        kuttResponse.setLink("https://short.link/abc123");
        kuttResponse.setTarget("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&openinapp=true&autoTriggerApp=true&fastrender=true&backstack=true");
        kuttResponse.setDescription("H000001");
        kuttResponse.setCreatedAt(new Date());
    }

    @Test
    void createReferralLink_ShouldIncludeTargetField() {
        // Given
        SystemParamEntity domainParam = new SystemParamEntity();
        domainParam.setCode(BuCodeEnum.HKTV.name());
        domainParam.setParmValue("https://www.hktvmall.com");

        when(findStoresUseCase.getStores(eq(userDto), any())).thenReturn(List.of(storeResponse));
        when(cacheHelper.findSystemParamBySegment(SystemParamEnum.REFERRAL_LINK_DOMAIN))
            .thenReturn(List.of(domainParam));
        when(kuttApiAdapter.createShortLink(eq(BuCodeEnum.HKTV), any(ShortLinkRequestDto.class)))
            .thenReturn(Optional.of(kuttResponse));

        // When
        ResponseDto<ReferralLinkPageResponse> result = referralLinkUseCase.createReferralLink(userDto, createRequest);

        // Then
        assertNotNull(result);
        assertEquals(ResponseStatusCode.SUCCESS.getCode(), result.getStatus());

        ReferralLinkPageResponse response = result.getData();
        assertNotNull(response);
        assertEquals("https://short.link/abc123", response.getLink());
        assertEquals("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&openinapp=true&autoTriggerApp=true&fastrender=true&backstack=true", response.getTarget());
        assertEquals("H000001", response.getStoreCode());
        assertNotNull(response.getCreatedTime());
    }

    @Test
    void getReferralLinkPage_ShouldIncludeTargetFieldForAllItems() {
        // Given
        ReferralLinkDetailResponseDto item1 = new ReferralLinkDetailResponseDto();
        item1.setId("id1");
        item1.setLink("https://short.link/item1");
        item1.setTarget("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&utm_source=google&utm_medium=cpc");
        item1.setDescription("H000001");
        item1.setCreatedAt(new Date());

        ReferralLinkDetailResponseDto item2 = new ReferralLinkDetailResponseDto();
        item2.setId("id2");
        item2.setLink("https://short.link/item2");
        item2.setTarget("https://www.hktvmall.com/s/H000002?utm_campaign=attr_H000002&utm_source=facebook&utm_medium=social");
        item2.setDescription("H000002");
        item2.setCreatedAt(new Date());

        ReferralLinkPageResponseDto pageResponse = new ReferralLinkPageResponseDto();
        pageResponse.setTotal(2);
        pageResponse.setData(List.of(item1, item2));

        FindStoresResponse storeResponse2 = FindStoresResponse.builder()
            .storeFrontStoreCode("H000002")
            .busUnitCode("HKTV")
            .build();

        when(findStoresUseCase.getStores(eq(userDto), any())).thenReturn(List.of(storeResponse, storeResponse2));
        when(kuttApiAdapter.getShortLink(eq(BuCodeEnum.HKTV), eq(0), eq(10), any()))
            .thenReturn(Optional.of(pageResponse));

        // When
        ResponseDto<PageResult<ReferralLinkPageResponse>> result =
            referralLinkUseCase.getReferralLinkPage(userDto, 10, 1, Set.of("H000001", "H000002"));

        // Then
        assertNotNull(result);
        assertEquals(ResponseStatusCode.SUCCESS.getCode(), result.getStatus());

        PageResult<ReferralLinkPageResponse> pageResult = result.getData();
        assertNotNull(pageResult);
        assertEquals(2, pageResult.getTotalItems());
        assertEquals(2, pageResult.getList().size());

        // Verify first item
        ReferralLinkPageResponse response1 = pageResult.getList().get(0);
        assertEquals("https://short.link/item1", response1.getLink());
        assertEquals("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&utm_source=google&utm_medium=cpc", response1.getTarget());
        assertEquals("H000001", response1.getStoreCode());

        // Verify second item
        ReferralLinkPageResponse response2 = pageResult.getList().get(1);
        assertEquals("https://short.link/item2", response2.getLink());
        assertEquals("https://www.hktvmall.com/s/H000002?utm_campaign=attr_H000002&utm_source=facebook&utm_medium=social", response2.getTarget());
        assertEquals("H000002", response2.getStoreCode());
    }

    @Test
    void createReferralLink_WithComplexUTMParameters_ShouldIncludeFullTargetURL() {
        // Given
        createRequest.setSources(List.of("google", "facebook"));
        createRequest.setMediums(List.of("cpc", "social"));
        createRequest.setCampaignDescription("summer_sale");
        createRequest.setCampaignDate("20240701");

        String expectedTarget = "https://www.hktvmall.com/s/H000001?utm_source=google+facebook&utm_medium=cpc+social&utm_campaign=attr_H000001_summer_sale_20240701&openinapp=true&autoTriggerApp=true&fastrender=true&backstack=true";

        kuttResponse.setTarget(expectedTarget);

        SystemParamEntity domainParam = new SystemParamEntity();
        domainParam.setCode(BuCodeEnum.HKTV.name());
        domainParam.setParmValue("https://www.hktvmall.com");

        when(findStoresUseCase.getStores(eq(userDto), any())).thenReturn(List.of(storeResponse));
        when(cacheHelper.findSystemParamBySegment(SystemParamEnum.REFERRAL_LINK_DOMAIN))
            .thenReturn(List.of(domainParam));
        when(kuttApiAdapter.createShortLink(eq(BuCodeEnum.HKTV), any(ShortLinkRequestDto.class)))
            .thenReturn(Optional.of(kuttResponse));

        // When
        ResponseDto<ReferralLinkPageResponse> result = referralLinkUseCase.createReferralLink(userDto, createRequest);

        // Then
        assertNotNull(result);
        assertEquals(ResponseStatusCode.SUCCESS.getCode(), result.getStatus());

        ReferralLinkPageResponse response = result.getData();
        assertNotNull(response);
        assertEquals("https://short.link/abc123", response.getLink());
        assertEquals(expectedTarget, response.getTarget());
        assertEquals("H000001", response.getStoreCode());
    }

    @Test
    void createReferralLink_WhenKuttApiReturnsError_ShouldReturnFailResponse() {
        // Given
        kuttResponse.setError("API Error");

        SystemParamEntity domainParam = new SystemParamEntity();
        domainParam.setCode(BuCodeEnum.HKTV.name());
        domainParam.setParmValue("https://www.hktvmall.com");

        when(findStoresUseCase.getStores(eq(userDto), any())).thenReturn(List.of(storeResponse));
        when(cacheHelper.findSystemParamBySegment(SystemParamEnum.REFERRAL_LINK_DOMAIN))
            .thenReturn(List.of(domainParam));
        when(kuttApiAdapter.createShortLink(eq(BuCodeEnum.HKTV), any(ShortLinkRequestDto.class)))
            .thenReturn(Optional.of(kuttResponse));
        when(messageSource.getMessage(eq("message23"), any(), isNull()))
            .thenReturn("Error occurred: API Error");

        // When
        ResponseDto<ReferralLinkPageResponse> result = referralLinkUseCase.createReferralLink(userDto, createRequest);

        // Then
        assertNotNull(result);
        assertEquals(ResponseStatusCode.FAIL.getCode(), result.getStatus());
        assertEquals("Error occurred: API Error", result.getMessage());
    }

    @Test
    void createReferralLink_WithNullTarget_ShouldHandleGracefully() {
        // Given
        kuttResponse.setTarget(null);

        SystemParamEntity domainParam = new SystemParamEntity();
        domainParam.setCode(BuCodeEnum.HKTV.name());
        domainParam.setParmValue("https://www.hktvmall.com");

        when(findStoresUseCase.getStores(eq(userDto), any())).thenReturn(List.of(storeResponse));
        when(cacheHelper.findSystemParamBySegment(SystemParamEnum.REFERRAL_LINK_DOMAIN))
            .thenReturn(List.of(domainParam));
        when(kuttApiAdapter.createShortLink(eq(BuCodeEnum.HKTV), any(ShortLinkRequestDto.class)))
            .thenReturn(Optional.of(kuttResponse));

        // When
        ResponseDto<ReferralLinkPageResponse> result = referralLinkUseCase.createReferralLink(userDto, createRequest);

        // Then
        assertNotNull(result);
        assertEquals(ResponseStatusCode.SUCCESS.getCode(), result.getStatus());

        ReferralLinkPageResponse response = result.getData();
        assertNotNull(response);
        assertEquals("https://short.link/abc123", response.getLink());
        assertNull(response.getTarget());
        assertEquals("H000001", response.getStoreCode());
    }

    @Test
    void exportReferralLinks_ShouldIncludeTargetLinkColumn() throws IOException {
        // Given
        ReferralLinkDetailResponseDto item1 = new ReferralLinkDetailResponseDto();
        item1.setId("id1");
        item1.setLink("https://short.link/item1");
        item1.setTarget("https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&utm_source=google&utm_medium=cpc");
        item1.setDescription("H000001");
        item1.setCreatedAt(new Date());

        ReferralLinkPageResponseDto pageResponse = new ReferralLinkPageResponseDto();
        pageResponse.setTotal(1);
        pageResponse.setData(List.of(item1));

        ReferralLinkExportRequest exportRequest = new ReferralLinkExportRequest();
        exportRequest.setStorefrontStoreCodes(List.of("H000001"));

        when(findStoresUseCase.getStores(eq(userDto), any())).thenReturn(List.of(storeResponse));
        when(kuttApiAdapter.getShortLink(eq(BuCodeEnum.HKTV), isNull(), isNull(), any()))
            .thenReturn(Optional.of(pageResponse));

        // When
        HttpEntity<ByteArrayResource> result = referralLinkUseCase.exportReferralLinks(userDto, exportRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getBody());
        assertTrue(result.getBody().contentLength() > 0);

        // Verify that the TARGET_LINK column is included in the enum
        assertEquals(2, ReferralLinkExcelColumnEnum.TARGET_LINK.getColumnNumber());
        assertEquals("Target Link", ReferralLinkExcelColumnEnum.TARGET_LINK.getColName());
    }

    @Test
    void getUrlInfo_WithQueryParameterWithoutValue_ShouldHandleGracefully() {
        // Given - URL with query parameter that has no '=' sign
        String urlWithInvalidParam = "https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&invalidParam&utm_source=google";

        // When - Should handle gracefully without throwing exception
        var result = referralLinkUseCase.getUrlInfo(urlWithInvalidParam);

        // Then
        assertNotNull(result);
        assertEquals("https://www.hktvmall.com/s/H000001", result.getLeft());

        Map<String, String> queryParams = result.getRight();
        assertEquals("attr_H000001", queryParams.get("utm_campaign"));
        assertEquals("google", queryParams.get("utm_source"));
        // invalidParam should be ignored since it has no value
        assertFalse(queryParams.containsKey("invalidParam"));
    }

    @Test
    void getUrlInfo_WithQueryParameterWithoutValue_ShouldHandleGracefully_AfterFix() {
        // Given - URL with query parameter that has no '=' sign
        String urlWithInvalidParam = "https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&invalidParam&utm_source=google";

        // When - should not throw exception
        var result = referralLinkUseCase.getUrlInfo(urlWithInvalidParam);

        // Then
        assertNotNull(result);
        assertEquals("https://www.hktvmall.com/s/H000001", result.getLeft());

        Map<String, String> queryParams = result.getRight();
        assertEquals("attr_H000001", queryParams.get("utm_campaign"));
        assertEquals("google", queryParams.get("utm_source"));
        // invalidParam should be ignored since it has no value
        assertFalse(queryParams.containsKey("invalidParam"));
    }

    @Test
    void getUrlInfo_WithEmptyQueryParameter_ShouldHandleGracefully() {
        // Given - URL with empty query parameter (key=)
        String urlWithEmptyParam = "https://www.hktvmall.com/s/H000001?utm_campaign=attr_H000001&emptyParam=&utm_source=google";

        // When
        var result = referralLinkUseCase.getUrlInfo(urlWithEmptyParam);

        // Then
        assertNotNull(result);
        assertEquals("https://www.hktvmall.com/s/H000001", result.getLeft());

        Map<String, String> queryParams = result.getRight();
        assertEquals("attr_H000001", queryParams.get("utm_campaign"));
        assertEquals("google", queryParams.get("utm_source"));
        assertEquals("", queryParams.get("emptyParam")); // Empty value should be preserved
    }

    @Test
    void getUrlInfo_WithNullUrl_ShouldReturnNullAndEmptyMap() {
        // When
        var result = referralLinkUseCase.getUrlInfo(null);

        // Then
        assertNotNull(result);
        assertNull(result.getLeft());
        assertTrue(result.getRight().isEmpty());
    }

    @Test
    void getUrlInfo_WithInvalidUrl_ShouldReturnNullAndEmptyMap() {
        // Given - malformed URL
        String invalidUrl = "http://[invalid-ipv6]";

        // When
        var result = referralLinkUseCase.getUrlInfo(invalidUrl);

        // Then
        assertNotNull(result);
        assertNull(result.getLeft());
        assertTrue(result.getRight().isEmpty());
    }
}
