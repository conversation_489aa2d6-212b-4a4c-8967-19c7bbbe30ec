apiVersion: v1
kind: ConfigMap
metadata:
  name: mms-store-configmap
  namespace: mms
data:
  java_opts: '-server -XX:+UseG1GC -verbose:gc -Xlog:gc:stdout -XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=90 -XX:MinRAMPercentage=50'
  glowroot_address: 'http://glowroot-service.shoalter-iids.svc.cluster.local:8181'
  active_profile: 'dev'
  little_mall_base_url: 'https://littlemall-internal-dev.hkmpcl.com.hk'
  little_mall_default_store_logo: 'https://cdn-media-dev.hkmpcl.com.hk/dev-hktv/StoreLogoPlaceholder.png'
  little_mall_jwt_env: 'DEV'
  mms_product_base_url: 'http://mms-product-api-svc.mms.svc.cluster.local:8080'
  mms_user_base_url: 'http://mms-user-api-svc.mms.svc.cluster.local:8080'
  image_upload_base_url: 'https://cdn-media-upload-dev.hkmpcl.com.hk'
  mms_chat_system_param_segment: 'CUSTOMER_CHAT_WHITELIST'
  mms_chat_white_list_cache_ttl: "300000"
  hktv_group_chat_base_url: 'https://group-chat-hktv-api-dev.hkmpcl.com.hk'
  customer_chat_base_url: 'https://mmschat-api-dev.hkmpcl.com.hk'
  hybris_base_url: 'https://ecomtest01.hkmpcl.com.hk/hktvwebservices'
  affiliate_impact_url: 'https://api.impact.com'
  affiliate_impact_catalogs_path: '/Mediapartners/{username}/Catalogs'
  affiliate_impact_username: 'IRTFgDTqMyhE5400398MWbNkWpLWg26iu1'
  affiliate_impact_token: 'YZLL4NeFHRE_RPKoVxHsa_itaDqmqB7N'
  gcp_c_name: 'theplace-cname-dev.hkmpcl.com.hk'
  cert_map_name: 'theplace-cert-maps'
  little_mall_domain: 'littlemall-dev.hkmpcl.com.hk'
  mdb_virtual_store_url: 'https://mdb-api-dev.hkmpcl.com.hk/s2s/api/v1/virtual_store/virtual_store_list/mms'
  message_center_url: 'http://shoalter-merchant-notification-system-svc.shoalter-merchant-notification-system.svc.cluster.local'
  kutt_base_url: 'https://hktv-kutt-dev.hkmpcl.com.hk'
  image_validation_max_file_size: '8MB'
  image_validation_allowed_extensions: 'jpg,jpeg,png'
  image_validation_max_file_name_length: '255'
  database_query_slow_query_threshold_ms: '100'
